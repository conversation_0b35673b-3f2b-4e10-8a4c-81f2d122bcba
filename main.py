#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2D双足行走强化学习仿真环境
基于Pygame和Pymunk实现的火柴人行走仿真
"""

import pygame
import pymunk
import pymunk.pygame_util
import numpy as np
import math
import json
import os
import time
from collections import deque
# matplotlib导入已移除，使用pygame原生绘图

class WalkingEnvironment:
    """2D双足行走强化学习环境"""

    def __init__(self, instance_id=0, shared_screen=None, shared_space=None, offset_x=0, offset_y=0, scale=1.0, color_alpha=255):
        """初始化环境
        Args:
            instance_id: 实例ID
            shared_screen: 共享的pygame屏幕对象
            shared_space: 共享的pymunk物理空间
            offset_x, offset_y: 渲染偏移量
            scale: 缩放比例
            color_alpha: 颜色透明度(0-255)
        """
        # 实例标识
        self.instance_id = instance_id
        self.shared_screen = shared_screen
        self.shared_space = shared_space
        self.offset_x = offset_x
        self.offset_y = offset_y
        self.scale = scale
        self.color_alpha = color_alpha

        # 窗口设置
        self.width = 1280
        self.height = 720
        self.fps = 60

        # 如果没有共享屏幕，则创建独立窗口
        if shared_screen is None:
            pygame.init()
            self.screen = pygame.display.set_mode((self.width, self.height))
            pygame.display.set_caption(f"2D双足行走仿真 - 实例 {instance_id}")
            self.clock = pygame.time.Clock()
            self.is_main_instance = True
        else:
            self.screen = shared_screen
            self.clock = None
            self.is_main_instance = False
        
        # 颜色定义
        self.WHITE = (255, 255, 255)
        self.BLACK = (0, 0, 0)
        self.RED = (255, 0, 0)
        self.BLUE = (0, 0, 255)
        self.GREEN = (0, 255, 0)
        self.GRAY = (128, 128, 128)
        
        # 物理世界设置
        if shared_space is None:
            # 创建新的物理空间（主实例或独立实例）
            self.space = pymunk.Space()
            self.space.gravity = (0, 981)  # 重力加速度
            self.is_space_owner = True
            # 创建地面
            self._create_ground()
        else:
            # 使用共享物理空间
            self.space = shared_space
            self.is_space_owner = False
            # 地面已由主实例创建，设置地面Y坐标
            self.ground_y = self.height - 50
        
        # 代理相关
        self.agent_body = None
        self.agent_parts = {}
        self.joints = {}
        self.motors = {}
        
        # 仿真参数
        self.dt = 1.0 / self.fps
        # 为不同实例设置不同的最大步数，增加训练多样性
        if shared_space is not None:  # 多实例模式
            # 主实例使用标准步数，其他实例使用变化的步数
            base_steps = 800
            self.max_steps = base_steps + (instance_id * 100)  # 800, 900, 1000, 1100...
        else:  # 单实例模式
            self.max_steps = 1000
        self.current_step = 0
        
        # 状态和动作空间
        self.action_dim = 4  # 左髋、左膝、右髋、右膝
        self.state_dim = 16  # 状态维度
        
        # 调试绘制工具
        self.draw_options = pymunk.pygame_util.DrawOptions(self.screen)

        # 训练数据管理
        self.data_dir = f"training_data/instance_{instance_id}"
        self.ensure_data_directory()

        # 训练历史记录
        self.episode_rewards = deque(maxlen=1000)  # 最近1000回合的奖励
        self.episode_steps = deque(maxlen=1000)    # 最近1000回合的步数
        self.episode_distances = deque(maxlen=1000) # 最近1000回合的前进距离

        # 训练统计
        self.total_episodes = 0
        self.training_start_time = time.time()
        self.current_episode_reward = 0
        self.current_episode_start_time = time.time()
        self.best_reward = float('-inf')
        self.best_distance = 0

        # 训练状态
        self.training_status = "训练中"  # 训练中/暂停/完成
        self.is_training = True

        # 终止条件严格程度 (0=宽松, 1=标准, 2=严格)
        self.termination_strictness = 1

        # 可视化相关
        self.chart_update_interval = 10  # 每10回合更新一次图表
        self.chart_surface = None
        self.chart_width = 400
        self.chart_height = 300
        
    def ensure_data_directory(self):
        """确保训练数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)

    def save_training_data(self):
        """保存训练数据"""
        data = {
            'episode_rewards': list(self.episode_rewards),
            'episode_steps': list(self.episode_steps),
            'episode_distances': list(self.episode_distances),
            'total_episodes': self.total_episodes,
            'best_reward': self.best_reward,
            'best_distance': self.best_distance,
            'training_time': time.time() - self.training_start_time
        }

        filename = os.path.join(self.data_dir, f"training_data_{int(time.time())}.json")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

    def load_training_data(self, filename=None):
        """加载训练数据"""
        if filename is None:
            # 查找最新的训练数据文件
            data_files = [f for f in os.listdir(self.data_dir) if f.startswith('training_data_') and f.endswith('.json')]
            if not data_files:
                return False
            filename = max(data_files)

        filepath = os.path.join(self.data_dir, filename)
        if not os.path.exists(filepath):
            return False

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.episode_rewards = deque(data.get('episode_rewards', []), maxlen=1000)
            self.episode_steps = deque(data.get('episode_steps', []), maxlen=1000)
            self.episode_distances = deque(data.get('episode_distances', []), maxlen=1000)
            self.total_episodes = data.get('total_episodes', 0)
            self.best_reward = data.get('best_reward', float('-inf'))
            self.best_distance = data.get('best_distance', 0)

            return True
        except Exception as e:
            print(f"加载训练数据失败: {e}")
            return False

    def record_episode_end(self, total_reward, total_steps, final_distance):
        """记录回合结束数据"""
        self.episode_rewards.append(total_reward)
        self.episode_steps.append(total_steps)
        self.episode_distances.append(final_distance)
        self.total_episodes += 1

        # 更新最佳记录
        if total_reward > self.best_reward:
            self.best_reward = total_reward
        if final_distance > self.best_distance:
            self.best_distance = final_distance

        # 定期保存数据
        if self.total_episodes % 50 == 0:
            self.save_training_data()

    def get_training_stats(self):
        """获取训练统计信息"""
        if not self.episode_rewards:
            return {
                'avg_reward': 0,
                'avg_steps': 0,
                'avg_distance': 0,
                'success_rate': 0,
                'recent_avg_reward': 0,
                'total_episodes': self.total_episodes,
                'training_time': time.time() - self.training_start_time
            }

        recent_rewards = list(self.episode_rewards)[-100:]  # 最近100回合
        recent_distances = list(self.episode_distances)[-100:]

        # 成功率定义：前进距离超过200像素
        success_count = sum(1 for d in recent_distances if d > 200)
        success_rate = success_count / len(recent_distances) if recent_distances else 0

        return {
            'avg_reward': np.mean(self.episode_rewards) if self.episode_rewards else 0,
            'avg_steps': np.mean(self.episode_steps) if self.episode_steps else 0,
            'avg_distance': np.mean(self.episode_distances) if self.episode_distances else 0,
            'success_rate': success_rate,
            'recent_avg_reward': np.mean(recent_rewards) if recent_rewards else 0,
            'total_episodes': self.total_episodes,
            'training_time': time.time() - self.training_start_time
        }

    def _create_ground(self):
        """创建静态地面"""
        ground_body = pymunk.Body(body_type=pymunk.Body.STATIC)
        ground_shape = pymunk.Segment(ground_body, (0, self.height - 50), (self.width, self.height - 50), 5)
        ground_shape.friction = 1.0
        ground_shape.collision_type = 1  # 地面碰撞类型
        self.space.add(ground_body, ground_shape)
        self.ground_y = self.height - 50
        
    def _create_agent(self, x=None, y=None):
        """创建火柴人代理"""
        # 如果没有指定位置，使用默认位置加上实例偏移
        if x is None:
            x = 100 + self.instance_id * 50  # 每个实例间隔50像素
        if y is None:
            y = 300
        """创建火柴人代理"""
        # 清除现有代理
        self._remove_agent()
        
        # 身体部件尺寸
        torso_length = 80
        thigh_length = 60
        shin_length = 60
        head_radius = 15
        
        # 质量参数
        torso_mass = 10
        thigh_mass = 5
        shin_mass = 3
        head_mass = 2
        
        # 创建躯干
        torso_body = pymunk.Body()
        torso_shape = pymunk.Segment(torso_body, (0, -torso_length/2), (0, torso_length/2), 3)
        torso_shape.mass = torso_mass
        torso_shape.friction = 0.7
        # 设置碰撞类型，确保不同实例的代理不会碰撞
        torso_shape.collision_type = self.instance_id + 2  # 地面是1，实例从2开始
        torso_shape.filter = pymunk.ShapeFilter(categories=1 << self.instance_id)
        torso_body.position = x, y
        self.space.add(torso_body, torso_shape)
        self.agent_parts['torso'] = (torso_body, torso_shape)
        
        # 创建头部
        head_body = pymunk.Body()
        head_shape = pymunk.Circle(head_body, head_radius)
        head_shape.mass = head_mass
        head_shape.friction = 0.7
        # 设置碰撞类型和过滤器
        head_shape.collision_type = self.instance_id + 2
        head_shape.filter = pymunk.ShapeFilter(categories=1 << self.instance_id)
        head_body.position = x, y - torso_length/2 - head_radius
        self.space.add(head_body, head_shape)
        self.agent_parts['head'] = (head_body, head_shape)
        
        # 连接头部和躯干
        head_joint = pymunk.PinJoint(torso_body, head_body, (0, -torso_length/2), (0, head_radius))
        self.space.add(head_joint)
        
        # 创建大腿
        left_thigh_body = pymunk.Body()
        left_thigh_shape = pymunk.Segment(left_thigh_body, (0, 0), (0, thigh_length), 3)
        left_thigh_shape.mass = thigh_mass
        left_thigh_shape.friction = 0.7
        # 设置碰撞类型和过滤器
        left_thigh_shape.collision_type = self.instance_id + 2
        left_thigh_shape.filter = pymunk.ShapeFilter(categories=1 << self.instance_id)
        left_thigh_body.position = x, y + torso_length/2
        self.space.add(left_thigh_body, left_thigh_shape)
        self.agent_parts['left_thigh'] = (left_thigh_body, left_thigh_shape)

        right_thigh_body = pymunk.Body()
        right_thigh_shape = pymunk.Segment(right_thigh_body, (0, 0), (0, thigh_length), 3)
        right_thigh_shape.mass = thigh_mass
        right_thigh_shape.friction = 0.7
        # 设置碰撞类型和过滤器
        right_thigh_shape.collision_type = self.instance_id + 2
        right_thigh_shape.filter = pymunk.ShapeFilter(categories=1 << self.instance_id)
        right_thigh_body.position = x, y + torso_length/2
        self.space.add(right_thigh_body, right_thigh_shape)
        self.agent_parts['right_thigh'] = (right_thigh_body, right_thigh_shape)
        
        # 创建小腿
        left_shin_body = pymunk.Body()
        left_shin_shape = pymunk.Segment(left_shin_body, (0, 0), (0, shin_length), 3)
        left_shin_shape.mass = shin_mass
        left_shin_shape.friction = 0.7
        # 设置碰撞类型和过滤器
        left_shin_shape.collision_type = self.instance_id + 2
        left_shin_shape.filter = pymunk.ShapeFilter(categories=1 << self.instance_id)
        left_shin_body.position = x, y + torso_length/2 + thigh_length
        self.space.add(left_shin_body, left_shin_shape)
        self.agent_parts['left_shin'] = (left_shin_body, left_shin_shape)

        right_shin_body = pymunk.Body()
        right_shin_shape = pymunk.Segment(right_shin_body, (0, 0), (0, shin_length), 3)
        right_shin_shape.mass = shin_mass
        right_shin_shape.friction = 0.7
        # 设置碰撞类型和过滤器
        right_shin_shape.collision_type = self.instance_id + 2
        right_shin_shape.filter = pymunk.ShapeFilter(categories=1 << self.instance_id)
        right_shin_body.position = x, y + torso_length/2 + thigh_length
        self.space.add(right_shin_body, right_shin_shape)
        self.agent_parts['right_shin'] = (right_shin_body, right_shin_shape)
        
        # 创建关节
        self._create_joints()
        
    def _create_joints(self):
        """创建关节和电机"""
        torso_body = self.agent_parts['torso'][0]
        left_thigh_body = self.agent_parts['left_thigh'][0]
        right_thigh_body = self.agent_parts['right_thigh'][0]
        left_shin_body = self.agent_parts['left_shin'][0]
        right_shin_body = self.agent_parts['right_shin'][0]
        
        # 髋关节
        left_hip = pymunk.PivotJoint(torso_body, left_thigh_body, (0, 40), (0, 0))
        right_hip = pymunk.PivotJoint(torso_body, right_thigh_body, (0, 40), (0, 0))
        
        # 膝关节
        left_knee = pymunk.PivotJoint(left_thigh_body, left_shin_body, (0, 60), (0, 0))
        right_knee = pymunk.PivotJoint(right_thigh_body, right_shin_body, (0, 60), (0, 0))
        
        self.space.add(left_hip, right_hip, left_knee, right_knee)
        self.joints = {
            'left_hip': left_hip,
            'right_hip': right_hip,
            'left_knee': left_knee,
            'right_knee': right_knee
        }
        
        # 创建电机
        left_hip_motor = pymunk.SimpleMotor(torso_body, left_thigh_body, 0)
        right_hip_motor = pymunk.SimpleMotor(torso_body, right_thigh_body, 0)
        left_knee_motor = pymunk.SimpleMotor(left_thigh_body, left_shin_body, 0)
        right_knee_motor = pymunk.SimpleMotor(right_thigh_body, right_shin_body, 0)
        
        self.space.add(left_hip_motor, right_hip_motor, left_knee_motor, right_knee_motor)
        self.motors = {
            'left_hip': left_hip_motor,
            'right_hip': right_hip_motor,
            'left_knee': left_knee_motor,
            'right_knee': right_knee_motor
        }
        
    def _remove_agent(self):
        """移除现有代理"""
        # 移除电机
        for motor in self.motors.values():
            if motor in self.space.constraints:
                self.space.remove(motor)
        self.motors.clear()
        
        # 移除关节
        for joint in self.joints.values():
            if joint in self.space.constraints:
                self.space.remove(joint)
        self.joints.clear()
        
        # 移除身体部件
        for body, shape in self.agent_parts.values():
            if shape in self.space.shapes:
                self.space.remove(shape)
            if body in self.space.bodies:
                self.space.remove(body)
        self.agent_parts.clear()

    def reset(self):
        """重置环境"""
        # 记录上一回合的数据（如果不是第一次重置）
        if hasattr(self, 'current_episode_reward') and self.current_step > 0:
            final_distance = 0
            if 'torso' in self.agent_parts:
                final_distance = max(0, self.agent_parts['torso'][0].position.x - 100)

            self.record_episode_end(
                self.current_episode_reward,
                self.current_step,
                final_distance
            )

        # 重置环境状态
        self.current_step = 0
        self.current_episode_reward = 0
        self.current_episode_start_time = time.time()
        self._create_agent()
        return self._get_state()

    def step(self, action):
        """执行一步仿真"""
        # 限制动作范围
        action = np.clip(action, -1, 1)

        # 应用动作到电机
        motor_names = ['left_hip', 'right_hip', 'left_knee', 'right_knee']
        for i, motor_name in enumerate(motor_names):
            if motor_name in self.motors:
                # 将动作转换为角速度 (范围: -10 到 10 rad/s)
                target_rate = action[i] * 10.0
                self.motors[motor_name].rate = target_rate

        # 推进物理仿真
        self.space.step(self.dt)
        self.current_step += 1

        # 获取新状态
        state = self._get_state()

        # 计算奖励
        reward = self._calculate_reward(action)
        self.current_episode_reward += reward

        # 检查终止条件
        done = self._is_done()

        # 信息字典
        info = {
            'step': self.current_step,
            'torso_x': self.agent_parts['torso'][0].position.x if 'torso' in self.agent_parts else 0,
            'episode_reward': self.current_episode_reward,
            'training_stats': self.get_training_stats()
        }

        return state, reward, done, info

    def _get_state(self):
        """获取当前状态"""
        if 'torso' not in self.agent_parts:
            return np.zeros(self.state_dim)

        state = []

        # 躯干信息
        torso_body = self.agent_parts['torso'][0]
        state.extend([
            torso_body.angle,  # 躯干角度
            torso_body.angular_velocity,  # 躯干角速度
            torso_body.position.x / 100.0,  # 躯干x位置(归一化)
            torso_body.position.y / 100.0,  # 躯干y位置(归一化)
            torso_body.velocity.x / 100.0,  # 躯干x速度(归一化)
            torso_body.velocity.y / 100.0   # 躯干y速度(归一化)
        ])

        # 关节角度和角速度
        joint_pairs = [
            ('torso', 'left_thigh'),
            ('torso', 'right_thigh'),
            ('left_thigh', 'left_shin'),
            ('right_thigh', 'right_shin')
        ]

        for parent_name, child_name in joint_pairs:
            if parent_name in self.agent_parts and child_name in self.agent_parts:
                parent_body = self.agent_parts[parent_name][0]
                child_body = self.agent_parts[child_name][0]

                # 计算相对角度
                relative_angle = child_body.angle - parent_body.angle
                relative_angular_velocity = child_body.angular_velocity - parent_body.angular_velocity

                state.extend([relative_angle, relative_angular_velocity])
            else:
                state.extend([0, 0])

        # 脚部接地状态
        left_foot_contact = self._check_foot_contact('left_shin')
        right_foot_contact = self._check_foot_contact('right_shin')
        state.extend([float(left_foot_contact), float(right_foot_contact)])

        return np.array(state, dtype=np.float32)

    def _check_foot_contact(self, shin_name):
        """检查脚部是否接触地面"""
        if shin_name not in self.agent_parts:
            return False

        shin_body = self.agent_parts[shin_name][0]

        # 检查小腿底部是否接近地面
        shin_bottom_y = shin_body.position.y + 60  # 小腿长度
        return shin_bottom_y >= self.ground_y - 10

    def _calculate_reward(self, action):
        """计算奖励 - 优化版本，强化前进速度奖励"""
        if 'torso' not in self.agent_parts:
            return -10

        torso_body = self.agent_parts['torso'][0]

        # 前进速度奖励 (大幅增加权重，鼓励快速前进)
        forward_velocity = torso_body.velocity.x
        velocity_reward = forward_velocity * 0.7  # 从0.1增加到0.5

        # 前进距离奖励 (额外奖励累积前进距离)
        distance_reward = max(0, torso_body.position.x - 100) * 0.1  # 起始位置x=100

        # 生存奖励 (保持不变)
        survival_reward = 6.0

        # 动作惩罚 (大幅降低权重，允许更大幅度动作)
        action_penalty = -0.007 * np.sum(action ** 2)  # 从-0.001降低到-0.0001

        # 保持直立奖励 (降低权重，减少对稳定性的过度关注)
        upright_reward = (1.0 - abs(torso_body.angle) * 0.3) * 0.3  # 权重从1.0降低到0.2

        # 速度方向奖励 (奖励向前的速度分量)
        if forward_velocity > 0:
            direction_bonus = 3.5  # 额外的前进方向奖励
        else:
            direction_bonus = -3.0  # 后退惩罚

        total_reward = (velocity_reward + distance_reward + survival_reward +
                       action_penalty + upright_reward + direction_bonus)

        return total_reward

    def _is_done(self):
        """检查是否终止 - 可调节严格程度的终止条件"""
        # 超过最大步数
        if self.current_step >= self.max_steps:
            return True

        if 'torso' not in self.agent_parts or 'head' not in self.agent_parts:
            return True

        torso_body = self.agent_parts['torso'][0]
        head_body = self.agent_parts['head'][0]

        # 根据严格程度调整终止条件（已移除躯干倾斜失败条件）
        if self.termination_strictness == 0:  # 宽松模式
            # 1. 头部深度接触地面
            if head_body.position.y >= self.ground_y + 5:
                return True
            # 2. 躯干深度接触地面
            if torso_body.position.y >= self.ground_y - 10:
                return True

        elif self.termination_strictness == 1:  # 标准模式（当前设置）
            # 1. 头部接触地面
            if head_body.position.y >= self.ground_y - 5:
                return True
            # 2. 躯干接触地面
            if torso_body.position.y >= self.ground_y - 20:
                return True

        else:  # 严格模式 (strictness == 2)
            # 1. 头部接近地面
            if head_body.position.y >= self.ground_y - 15:
                return True
            # 2. 躯干接近地面
            if torso_body.position.y >= self.ground_y - 30:
                return True

        # 通用终止条件（所有模式）
        # 4. 代理移动过远
        if torso_body.position.x > 2000 or torso_body.position.x < -200:
            return True

        # 5. 代理掉落过深
        if torso_body.position.y > self.ground_y + 100:
            return True

        # 注意：已移除速度过大的失败条件，允许更激进的动作探索

        return False

    def render(self, clear_screen=None):
        """渲染环境"""
        # 只有主实例或明确指定时才清屏
        if clear_screen is None:
            clear_screen = self.is_main_instance

        if clear_screen:
            self.screen.fill(self.WHITE)

        # 绘制地面（只有主实例绘制，或者是独立实例）
        if self.is_main_instance:
            ground_y = int((self.ground_y * self.scale) + self.offset_y)
            pygame.draw.line(self.screen, self.BLACK,
                            (self.offset_x, ground_y),
                            (int(self.width * self.scale) + self.offset_x, ground_y),
                            max(1, int(5 * self.scale)))

        # 绘制代理
        if self.agent_parts:
            self._draw_agent()

        # 只有主实例绘制调试信息
        if self.is_main_instance:
            self._draw_debug_info()

        # 只有主实例或独立实例才更新显示
        if self.is_main_instance:
            pygame.display.flip()

    def _draw_agent(self):
        """绘制代理"""
        # 绘制身体部件
        for part_name, (body, shape) in self.agent_parts.items():
            if isinstance(shape, pymunk.Segment):
                # 绘制线段(躯干、大腿、小腿)
                start_pos = body.local_to_world(shape.a)
                end_pos = body.local_to_world(shape.b)

                # 应用缩放和偏移
                start_pos = (int((start_pos.x * self.scale) + self.offset_x),
                           int((start_pos.y * self.scale) + self.offset_y))
                end_pos = (int((end_pos.x * self.scale) + self.offset_x),
                         int((end_pos.y * self.scale) + self.offset_y))

                # 根据部件类型选择颜色
                if 'torso' in part_name:
                    base_color = self.BLUE
                    width = max(1, int(6 * self.scale))
                elif 'thigh' in part_name:
                    base_color = self.GREEN
                    width = max(1, int(4 * self.scale))
                else:  # shin
                    base_color = self.RED
                    width = max(1, int(4 * self.scale))

                # 主实例(instance_id=0)使用正常颜色，其他实例使用更浅的颜色
                if self.instance_id == 0:
                    color = base_color
                else:
                    # 其他实例使用更浅、更透明的颜色
                    lightness_factor = 0.6 + (self.instance_id * 0.1)  # 逐渐变浅
                    color = tuple(min(255, int(c + (255 - c) * lightness_factor)) for c in base_color)

                pygame.draw.line(self.screen, color, start_pos, end_pos, width)

            elif isinstance(shape, pymunk.Circle):
                # 绘制圆形(头部)
                pos = (int((body.position.x * self.scale) + self.offset_x),
                      int((body.position.y * self.scale) + self.offset_y))
                radius = max(1, int(shape.radius * self.scale))

                # 主实例使用正常颜色，其他实例使用更浅的颜色
                if self.instance_id == 0:
                    head_color = self.BLACK
                else:
                    lightness_factor = 0.6 + (self.instance_id * 0.1)
                    head_color = tuple(min(255, int(c + (255 - c) * lightness_factor)) for c in self.BLACK)

                pygame.draw.circle(self.screen, head_color, pos, radius)

        # 绘制关节(红色圆圈)
        self._draw_joints()

    def _draw_joints(self):
        """绘制关节位置"""
        if 'torso' not in self.agent_parts:
            return

        torso_body = self.agent_parts['torso'][0]

        # 主实例使用正常颜色，其他实例使用更浅的颜色
        if self.instance_id == 0:
            joint_color = self.RED
        else:
            lightness_factor = 0.6 + (self.instance_id * 0.1)
            joint_color = tuple(min(255, int(c + (255 - c) * lightness_factor)) for c in self.RED)

        # 髋关节位置
        hip_pos = torso_body.local_to_world((0, 40))
        hip_pos = (int((hip_pos.x * self.scale) + self.offset_x),
                  int((hip_pos.y * self.scale) + self.offset_y))
        pygame.draw.circle(self.screen, joint_color, hip_pos, max(1, int(8 * self.scale)))

        # 膝关节位置
        if 'left_thigh' in self.agent_parts:
            left_thigh_body = self.agent_parts['left_thigh'][0]
            left_knee_pos = left_thigh_body.local_to_world((0, 60))
            left_knee_pos = (int((left_knee_pos.x * self.scale) + self.offset_x),
                           int((left_knee_pos.y * self.scale) + self.offset_y))
            pygame.draw.circle(self.screen, joint_color, left_knee_pos, max(1, int(6 * self.scale)))

        if 'right_thigh' in self.agent_parts:
            right_thigh_body = self.agent_parts['right_thigh'][0]
            right_knee_pos = right_thigh_body.local_to_world((0, 60))
            right_knee_pos = (int((right_knee_pos.x * self.scale) + self.offset_x),
                            int((right_knee_pos.y * self.scale) + self.offset_y))
            pygame.draw.circle(self.screen, joint_color, right_knee_pos, max(1, int(6 * self.scale)))

    def _draw_debug_info(self):
        """绘制增强的训练信息界面"""
        # 创建不同大小的字体(使用微软雅黑支持中文)
        try:
            title_font = pygame.font.Font("C:/Windows/Fonts/msyh.ttc", 48)
            large_font = pygame.font.Font("C:/Windows/Fonts/msyh.ttc", 36)
            medium_font = pygame.font.Font("C:/Windows/Fonts/msyh.ttc", 28)
            small_font = pygame.font.Font("C:/Windows/Fonts/msyh.ttc", 24)
        except:
            # 回退到默认字体
            title_font = pygame.font.Font(None, 48)
            large_font = pygame.font.Font(None, 36)
            medium_font = pygame.font.Font(None, 28)
            small_font = pygame.font.Font(None, 24)

        # 获取训练统计信息
        stats = self.get_training_stats()

        # 左侧信息面板
        y_offset = 10

        # 标题
        title_text = title_font.render("2D双足行走训练", True, self.BLUE)
        self.screen.blit(title_text, (10, y_offset))
        y_offset += 60

        # 训练状态指示器
        status_color = self.GREEN if self.is_training else self.RED
        status_text = large_font.render(f"状态: {self.training_status}", True, status_color)
        self.screen.blit(status_text, (10, y_offset))
        y_offset += 40

        # 当前回合信息
        episode_text = medium_font.render(f"当前回合: {self.total_episodes + 1}", True, self.BLACK)
        self.screen.blit(episode_text, (10, y_offset))
        y_offset += 30

        step_text = medium_font.render(f"当前步数: {self.current_step}", True, self.BLACK)
        self.screen.blit(step_text, (10, y_offset))
        y_offset += 30

        reward_text = medium_font.render(f"当前奖励: {self.current_episode_reward:.2f}", True, self.BLACK)
        self.screen.blit(reward_text, (10, y_offset))
        y_offset += 40

        # 代理状态信息
        if 'torso' in self.agent_parts:
            torso_body = self.agent_parts['torso'][0]
            distance = max(0, torso_body.position.x - 100)

            pos_text = small_font.render(f"位置: ({torso_body.position.x:.1f}, {torso_body.position.y:.1f})", True, self.BLACK)
            self.screen.blit(pos_text, (10, y_offset))
            y_offset += 25

            angle_text = small_font.render(f"角度: {math.degrees(torso_body.angle):.1f}°", True, self.BLACK)
            self.screen.blit(angle_text, (10, y_offset))
            y_offset += 25

            velocity_text = small_font.render(f"速度: {torso_body.velocity.x:.2f} px/s", True, self.BLACK)
            self.screen.blit(velocity_text, (10, y_offset))
            y_offset += 25

            distance_text = small_font.render(f"前进距离: {distance:.1f} px", True, self.BLACK)
            self.screen.blit(distance_text, (10, y_offset))
            y_offset += 40

        # 训练统计信息
        if stats['total_episodes'] > 0:
            stats_title = medium_font.render("训练统计", True, self.BLUE)
            self.screen.blit(stats_title, (10, y_offset))
            y_offset += 35

            total_episodes_text = small_font.render(f"总回合数: {stats['total_episodes']}", True, self.BLACK)
            self.screen.blit(total_episodes_text, (10, y_offset))
            y_offset += 25

            avg_reward_text = small_font.render(f"平均奖励: {stats['avg_reward']:.2f}", True, self.BLACK)
            self.screen.blit(avg_reward_text, (10, y_offset))
            y_offset += 25

            recent_reward_text = small_font.render(f"近期平均奖励: {stats['recent_avg_reward']:.2f}", True, self.BLACK)
            self.screen.blit(recent_reward_text, (10, y_offset))
            y_offset += 25

            success_rate_text = small_font.render(f"成功率: {stats['success_rate']*100:.1f}%", True, self.BLACK)
            self.screen.blit(success_rate_text, (10, y_offset))
            y_offset += 25

            best_reward_text = small_font.render(f"最佳奖励: {self.best_reward:.2f}", True, self.BLACK)
            self.screen.blit(best_reward_text, (10, y_offset))
            y_offset += 25

            best_distance_text = small_font.render(f"最远距离: {self.best_distance:.1f} px", True, self.BLACK)
            self.screen.blit(best_distance_text, (10, y_offset))
            y_offset += 25

            # 训练时间
            training_time = stats['training_time']
            hours = int(training_time // 3600)
            minutes = int((training_time % 3600) // 60)
            seconds = int(training_time % 60)
            time_text = small_font.render(f"训练时间: {hours:02d}:{minutes:02d}:{seconds:02d}", True, self.BLACK)
            self.screen.blit(time_text, (10, y_offset))

        # 绘制训练曲线图表
        self._draw_training_charts()

    def _draw_training_charts(self):
        """绘制训练曲线图表"""
        if len(self.episode_rewards) < 2:
            return

        # 图表位置和大小
        chart_x = self.width - self.chart_width - 20
        chart_y = 20

        # 绘制背景
        chart_rect = pygame.Rect(chart_x, chart_y, self.chart_width, self.chart_height)
        pygame.draw.rect(self.screen, (240, 240, 240), chart_rect)
        pygame.draw.rect(self.screen, self.BLACK, chart_rect, 2)

        # 图表标题
        title_font = pygame.font.Font(None, 24)
        title_text = title_font.render("奖励趋势", True, self.BLACK)
        self.screen.blit(title_text, (chart_x + 10, chart_y + 5))

        # 准备数据
        rewards = list(self.episode_rewards)[-100:]  # 最近100回合
        if len(rewards) < 2:
            return

        # 计算缩放参数
        min_reward = min(rewards)
        max_reward = max(rewards)
        reward_range = max_reward - min_reward if max_reward != min_reward else 1

        # 绘制区域
        plot_x = chart_x + 30
        plot_y = chart_y + 30
        plot_width = self.chart_width - 60
        plot_height = self.chart_height - 60

        # 绘制网格线
        grid_color = (200, 200, 200)
        for i in range(5):
            y = plot_y + i * plot_height // 4
            pygame.draw.line(self.screen, grid_color, (plot_x, y), (plot_x + plot_width, y), 1)

        # 绘制奖励曲线
        if len(rewards) > 1:
            points = []
            for i, reward in enumerate(rewards):
                x = plot_x + i * plot_width // (len(rewards) - 1)
                y = plot_y + plot_height - int((reward - min_reward) / reward_range * plot_height)
                points.append((x, y))

            if len(points) > 1:
                pygame.draw.lines(self.screen, self.BLUE, False, points, 2)

        # 绘制移动平均线
        if len(rewards) >= 10:
            window_size = min(10, len(rewards))
            moving_avg = []
            for i in range(len(rewards) - window_size + 1):
                avg = np.mean(rewards[i:i + window_size])
                moving_avg.append(avg)

            if moving_avg:
                avg_points = []
                for i, avg in enumerate(moving_avg):
                    x = plot_x + (i + window_size // 2) * plot_width // (len(rewards) - 1)
                    y = plot_y + plot_height - int((avg - min_reward) / reward_range * plot_height)
                    avg_points.append((x, y))

                if len(avg_points) > 1:
                    pygame.draw.lines(self.screen, self.RED, False, avg_points, 2)

        # 绘制Y轴标签
        label_font = pygame.font.Font(None, 18)
        for i in range(5):
            value = max_reward - i * reward_range / 4
            label = label_font.render(f"{value:.1f}", True, self.BLACK)
            y = plot_y + i * plot_height // 4 - 8
            self.screen.blit(label, (chart_x + 5, y))

        # 绘制图例
        legend_y = chart_y + self.chart_height - 25
        pygame.draw.line(self.screen, self.BLUE, (chart_x + 10, legend_y), (chart_x + 30, legend_y), 2)
        legend_text = label_font.render("奖励", True, self.BLACK)
        self.screen.blit(legend_text, (chart_x + 35, legend_y - 8))

        if len(rewards) >= 10:
            pygame.draw.line(self.screen, self.RED, (chart_x + 80, legend_y), (chart_x + 100, legend_y), 2)
            avg_text = label_font.render("移动平均", True, self.BLACK)
            self.screen.blit(avg_text, (chart_x + 105, legend_y - 8))

    def pause_training(self):
        """暂停训练"""
        self.is_training = False
        self.training_status = "暂停"

    def resume_training(self):
        """恢复训练"""
        self.is_training = True
        self.training_status = "训练中"

    def stop_training(self):
        """停止训练"""
        self.is_training = False
        self.training_status = "完成"
        self.save_training_data()

    def get_performance_summary(self):
        """获取性能总结"""
        if not self.episode_rewards:
            return "暂无训练数据"

        stats = self.get_training_stats()
        summary = f"""
训练总结:
- 总回合数: {stats['total_episodes']}
- 平均奖励: {stats['avg_reward']:.2f}
- 最佳奖励: {self.best_reward:.2f}
- 最远距离: {self.best_distance:.1f} px
- 成功率: {stats['success_rate']*100:.1f}%
- 训练时间: {stats['training_time']:.0f} 秒
"""
        return summary

    def close(self):
        """关闭环境"""
        # 保存最终训练数据
        if self.total_episodes > 0:
            self.save_training_data()
            print(self.get_performance_summary())

        # 只有主实例才关闭pygame
        if self.is_main_instance:
            pygame.quit()


def main():
    """主函数 - 增强版本，支持训练数据管理和可视化"""
    # 创建环境
    env = WalkingEnvironment()

    try:
        # 尝试加载之前的训练数据
        if env.load_training_data():
            print("已加载之前的训练数据")

        # 重置环境
        state = env.reset()
        print(f"初始状态维度: {len(state)}")
        print("控制说明:")
        print("- R: 重置环境")
        print("- P: 暂停/恢复训练")
        print("- S: 保存训练数据")
        print("- ESC: 退出程序")

        running = True
        episode = 1

        while running:
            # 处理事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_r:  # 按R重置
                        state = env.reset()
                        episode += 1
                        print(f"\n=== 第 {episode} 回合 ===")
                    elif event.key == pygame.K_p:  # 按P暂停/恢复
                        if env.is_training:
                            env.pause_training()
                            print("训练已暂停")
                        else:
                            env.resume_training()
                            print("训练已恢复")
                    elif event.key == pygame.K_s:  # 按S保存数据
                        env.save_training_data()
                        print("训练数据已保存")
                    elif event.key == pygame.K_ESCAPE:  # 按ESC退出
                        running = False

            # 只在训练状态下执行动作
            if env.is_training:
                # 生成随机动作(测试用)
                action = np.random.uniform(-0.8, 0.8, 4)  # 增大动作范围以配合新的奖励函数

                # 执行步骤
                state, reward, done, info = env.step(action)

                # 打印调试信息
                if env.current_step % 120 == 0:  # 每2秒打印一次
                    stats = info.get('training_stats', {})
                    print(f"回合 {env.total_episodes + 1}, 步数: {env.current_step}, "
                          f"奖励: {reward:.3f}, 累积奖励: {info.get('episode_reward', 0):.3f}, "
                          f"位置: {info.get('torso_x', 0):.1f}, 平均奖励: {stats.get('recent_avg_reward', 0):.2f}")

                # 检查终止
                if done:
                    final_distance = max(0, info.get('torso_x', 0) - 100)
                    print(f"回合 {env.total_episodes + 1} 结束! 步数: {env.current_step}, "
                          f"总奖励: {info.get('episode_reward', 0):.3f}, 前进距离: {final_distance:.1f}")

                    state = env.reset()
                    episode += 1

                    # 每10回合显示统计信息
                    if env.total_episodes % 10 == 0:
                        stats = env.get_training_stats()
                        print(f"\n=== 训练统计 (第 {env.total_episodes} 回合) ===")
                        print(f"平均奖励: {stats['avg_reward']:.2f}")
                        print(f"近期平均奖励: {stats['recent_avg_reward']:.2f}")
                        print(f"成功率: {stats['success_rate']*100:.1f}%")
                        print(f"最佳奖励: {env.best_reward:.2f}")
                        print(f"最远距离: {env.best_distance:.1f} px")

            # 渲染
            env.render()
            env.clock.tick(env.fps)

    except KeyboardInterrupt:
        print("\n用户中断")
    finally:
        env.stop_training()
        env.close()


class MultiTrainingManager:
    """多训练实例管理器"""

    def __init__(self, num_instances=4, show_all_in_one_window=True):
        """初始化多训练管理器
        Args:
            num_instances: 训练实例数量
            show_all_in_one_window: 是否在一个窗口中显示所有训练
        """
        self.num_instances = num_instances
        self.show_all_in_one_window = show_all_in_one_window
        self.environments = []

        # 窗口设置
        self.width = 1280
        self.height = 720
        self.fps = 60

        if show_all_in_one_window:
            # 初始化共享窗口
            pygame.init()
            self.shared_screen = pygame.display.set_mode((self.width, self.height))
            pygame.display.set_caption("2D双足行走多实例训练")
            self.clock = pygame.time.Clock()

            # 创建共享物理空间
            self.shared_space = pymunk.Space()
            self.shared_space.gravity = (0, 981)

            # 创建主实例（负责创建地面和管理物理空间）
            main_env = WalkingEnvironment(
                instance_id=0,
                shared_screen=self.shared_screen,
                shared_space=self.shared_space,
                offset_x=0,
                offset_y=0,
                scale=1.0,
                color_alpha=255
            )
            self.environments.append(main_env)

            # 创建其他实例（共享物理空间，不同位置由instance_id自动处理）
            for i in range(1, num_instances):
                env = WalkingEnvironment(
                    instance_id=i,
                    shared_screen=self.shared_screen,
                    shared_space=self.shared_space,
                    offset_x=0,
                    offset_y=0,
                    scale=1.0,
                    color_alpha=255
                )
                self.environments.append(env)
        else:
            # 创建独立窗口的环境实例
            for i in range(num_instances):
                env = WalkingEnvironment(instance_id=i)
                self.environments.append(env)

        # 为每个环境重置
        for env in self.environments:
            env.reset()

    def step_all(self):
        """对所有环境执行一步 - 优化的多实例训练"""
        results = []
        for i, env in enumerate(self.environments):
            if env.is_training:
                # 为不同实例生成略有差异的动作，增加探索多样性
                base_action = np.random.uniform(-0.8, 0.8, 4)
                # 为每个实例添加小的随机偏移，增加训练多样性
                action_noise = np.random.normal(0, 0.1, 4) * (i + 1) * 0.1
                action = np.clip(base_action + action_noise, -1.0, 1.0)

                state, reward, done, info = env.step(action)

                # 智能重置策略
                if done:
                    # 记录重置原因（用于调试）
                    reset_reason = self._get_reset_reason(env)
                    if i == 0:  # 只为主实例打印重置信息
                        print(f"实例 {i} 重置: {reset_reason}, 步数: {env.current_step}, 奖励: {env.current_episode_reward:.2f}")

                    state = env.reset()

                results.append((state, reward, done, info))
            else:
                results.append((None, 0, False, {}))
        return results

    def _get_reset_reason(self, env):
        """获取重置原因"""
        if env.current_step >= env.max_steps:
            return "达到最大步数"

        if 'torso' not in env.agent_parts:
            return "代理缺失"

        torso_body = env.agent_parts['torso'][0]
        head_body = env.agent_parts['head'][0]

        if head_body.position.y >= env.ground_y - 5:
            return "头部接地"
        elif torso_body.position.y >= env.ground_y - 20:
            return "躯干接地"
        elif torso_body.position.x > 2000 or torso_body.position.x < -200:
            return "移动过远"
        elif torso_body.position.y > env.ground_y + 100:
            return "掉落过深"

        return "未知原因"

    def render_all(self):
        """渲染所有环境"""
        if self.show_all_in_one_window:
            # 清屏
            self.shared_screen.fill((255, 255, 255))

            # 主实例负责绘制地面和调试信息
            self.environments[0].render(clear_screen=False)

            # 其他实例只绘制代理
            for i in range(1, len(self.environments)):
                env = self.environments[i]
                if env.agent_parts:
                    env._draw_agent()

            # 绘制实例状态信息
            font = pygame.font.Font(None, 20)
            y_start = 10

            for i, env in enumerate(self.environments):
                stats = env.get_training_stats()

                # 显示实例信息
                if 'torso' in env.agent_parts:
                    torso_x = env.agent_parts['torso'][0].position.x
                    text = f"实例 {i}: 回合 {stats['total_episodes']}, 奖励 {stats.get('recent_avg_reward', 0):.1f}, 位置 {torso_x:.0f}"
                else:
                    text = f"实例 {i}: 回合 {stats['total_episodes']}, 奖励 {stats.get('recent_avg_reward', 0):.1f}"

                color = (0, 120, 0) if env.is_training else (120, 0, 0)
                text_surface = font.render(text, True, color)
                self.shared_screen.blit(text_surface, (self.width - 400, y_start + i * 25))

            pygame.display.flip()
        else:
            # 独立窗口渲染
            for env in self.environments:
                env.render()

    def handle_events(self):
        """处理事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r:  # 重置所有环境
                    for env in self.environments:
                        env.reset()
                elif event.key == pygame.K_p:  # 暂停/恢复所有训练
                    for env in self.environments:
                        if env.is_training:
                            env.pause_training()
                        else:
                            env.resume_training()
                elif event.key == pygame.K_s:  # 保存所有数据
                    for env in self.environments:
                        env.save_training_data()
                    print("所有实例数据已保存")
                elif event.key == pygame.K_ESCAPE:
                    return False
                elif event.key == pygame.K_t:  # 按T调整终止条件严格程度
                    for env in self.environments:
                        env.termination_strictness = (env.termination_strictness + 1) % 3
                    strictness_names = ["宽松", "标准", "严格"]
                    print(f"终止条件已调整为: {strictness_names[self.environments[0].termination_strictness]}")
                elif event.key >= pygame.K_1 and event.key <= pygame.K_9:
                    # 切换单个实例的训练状态
                    instance_num = event.key - pygame.K_1
                    if instance_num < len(self.environments):
                        env = self.environments[instance_num]
                        if env.is_training:
                            env.pause_training()
                            print(f"实例 {instance_num} 已暂停")
                        else:
                            env.resume_training()
                            print(f"实例 {instance_num} 已恢复")
        return True

    def get_summary_stats(self):
        """获取所有实例的汇总统计"""
        total_episodes = sum(env.total_episodes for env in self.environments)
        avg_rewards = [env.get_training_stats()['recent_avg_reward'] for env in self.environments]
        best_rewards = [env.best_reward for env in self.environments]

        return {
            'total_episodes': total_episodes,
            'avg_recent_reward': np.mean(avg_rewards) if avg_rewards else 0,
            'best_reward': max(best_rewards) if best_rewards else 0,
            'active_instances': sum(1 for env in self.environments if env.is_training)
        }

    def close_all(self):
        """关闭所有环境"""
        for env in self.environments:
            env.stop_training()
            if not self.show_all_in_one_window:
                env.close()

        if self.show_all_in_one_window:
            pygame.quit()


def main_multi():
    """多实例训练主函数"""
    print("选择训练模式:")
    print("1. 单实例训练")
    print("2. 多实例训练 (独立窗口)")
    print("3. 多实例训练 (共享窗口)")

    choice = input("请输入选择 (1-3): ").strip()

    if choice == "1":
        main()
    elif choice == "2":
        num_instances = int(input("请输入实例数量 (1-8): ") or "4")
        manager = MultiTrainingManager(num_instances, show_all_in_one_window=False)
        run_multi_training(manager)
    elif choice == "3":
        num_instances = int(input("请输入实例数量 (1-9): ") or "4")
        manager = MultiTrainingManager(num_instances, show_all_in_one_window=True)
        run_multi_training(manager)
    else:
        print("无效选择，启动单实例训练")
        main()


def run_multi_training(manager):
    """运行多实例训练"""
    print(f"启动 {manager.num_instances} 个训练实例")
    print("控制说明:")
    print("- R: 重置所有环境")
    print("- P: 暂停/恢复所有训练")
    print("- S: 保存所有训练数据")
    print("- T: 调整终止条件严格程度 (宽松/标准/严格)")
    print("- 1-9: 切换对应实例的训练状态")
    print("- ESC: 退出程序")

    try:
        running = True
        step_count = 0

        while running:
            # 处理事件
            running = manager.handle_events()

            # 执行训练步骤
            manager.step_all()

            # 渲染
            manager.render_all()

            if manager.show_all_in_one_window:
                manager.clock.tick(manager.fps)

            # 定期打印统计信息
            step_count += 1
            if step_count % 600 == 0:  # 每10秒打印一次
                stats = manager.get_summary_stats()
                print(f"总回合: {stats['total_episodes']}, "
                      f"平均奖励: {stats['avg_recent_reward']:.2f}, "
                      f"最佳奖励: {stats['best_reward']:.2f}, "
                      f"活跃实例: {stats['active_instances']}/{manager.num_instances}")

    except KeyboardInterrupt:
        print("\n用户中断")
    finally:
        manager.close_all()


if __name__ == "__main__":
    main_multi()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终止条件测试脚本
"""

from main import WalkingEnvironment
import numpy as np

def test_termination_conditions():
    """测试不同严格程度的终止条件（已移除躯干倾斜和速度过大失败条件）"""
    print("测试终止条件...")
    print("注意：已移除躯干倾斜和速度过大失败条件，代理可以进行更激进的动作探索")

    strictness_names = ["宽松", "标准", "严格"]
    
    for strictness in range(3):
        print(f"\n=== 测试 {strictness_names[strictness]} 模式 ===")
        
        # 创建环境
        env = WalkingEnvironment()
        env.termination_strictness = strictness
        
        # 重置环境
        env.reset()

        episode_count = 0
        total_steps = 0

        # 运行5个回合进行测试
        for episode in range(5):
            env.reset()
            steps = 0
            done = False

            while not done and steps < 2000:  # 最大2000步防止无限循环
                # 生成随机动作
                action = np.random.uniform(-0.8, 0.8, 4)

                # 执行步骤
                _, _, done, _ = env.step(action)
                steps += 1
                
                # 渲染（可选，注释掉以加快测试）
                # env.render()
                # env.clock.tick(60)
                
            print(f"  回合 {episode + 1}: {steps} 步")
            total_steps += steps
            episode_count += 1
            
        avg_steps = total_steps / episode_count
        print(f"  平均步数: {avg_steps:.1f}")
        
        env.close()
        
    print("\n测试完成！")
    print("预期结果：宽松模式 > 标准模式 > 严格模式 的平均步数")

if __name__ == "__main__":
    test_termination_conditions()

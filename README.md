# 2D双足行走强化学习仿真环境

基于Pygame和Pymunk实现的2D火柴人行走仿真环境，兼容强化学习算法接口。

## 功能特点

- 基于物理引擎的真实2D双足行走仿真
- 类似OpenAI Gym的环境接口
- **多实例并行训练**：支持同时运行多个训练实例，共享物理空间但避免碰撞
- **增强的可视化界面**：实时显示训练进展、统计信息和奖励曲线
- **训练数据管理**：自动保存和加载训练历史
- **优化的奖励函数**：强化前进速度奖励，降低稳定性约束
- **实时训练监控**：显示成功率、平均奖励、最佳记录等关键指标
- **性能优化显示**：主实例正常颜色，其他实例浅色透明显示
- 完整的状态观测和奖励系统
- 支持强化学习算法训练

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行仿真

### 启动训练
```bash
python main.py
```

程序会提示选择训练模式：
1. 单实例训练
2. 多实例训练 (独立窗口)
3. 多实例训练 (共享窗口)

### 快速测试多实例
```bash
python test_multi.py
```

## 控制说明

### 单实例模式
- 按 `R` 键重置环境
- 按 `P` 键暂停/恢复训练
- 按 `S` 键保存训练数据
- 按 `ESC` 键或关闭窗口退出程序

### 多实例模式
- 按 `R` 键重置所有环境
- 按 `P` 键暂停/恢复所有训练
- 按 `S` 键保存所有训练数据
- 按 `T` 键调整终止条件严格程度（宽松/标准/严格）
- 按 `1-9` 键切换对应实例的训练状态
- 按 `ESC` 键或关闭窗口退出程序

## 环境接口

### 动作空间
- 4维连续动作空间，范围 [-1, 1]
- 分别对应：左髋、右髋、左膝、右膝的目标角速度

### 状态空间
- 16维状态向量，包含：
  - 躯干角度和角速度
  - 躯干位置和速度
  - 4个关节的相对角度和角速度
  - 双脚接地状态

### 奖励函数 (优化版本)
- **强化前进速度奖励** (权重 0.5)：鼓励快速前进
- **前进距离奖励**：累积前进距离的额外奖励
- **速度方向奖励**：额外奖励向前运动，惩罚后退
- 生存奖励：基础生存奖励
- **降低动作平滑性惩罚** (权重 0.0001)：允许更大幅度动作
- **降低直立性奖励** (权重 0.2)：减少对稳定性的过度关注

### 终止条件 (可调节严格程度)

#### 宽松模式
- 头部深度接触地面
- 躯干深度接触地面
- 达到最大步数限制

#### 标准模式 (默认)
- 头部接触地面
- 躯干接触地面
- 达到最大步数限制

#### 严格模式
- 头部接近地面
- 躯干接近地面
- 达到最大步数限制

#### 通用终止条件 (所有模式)
- 代理移动过远(超出边界)
- 代理掉落过深

## 代码结构

### 核心环境类
- `WalkingEnvironment`: 主要的环境类
- `_create_agent()`: 创建火柴人代理
- `_create_joints()`: 创建关节和电机系统
- `step()`: 执行一步仿真
- `render()`: 渲染可视化
- `reset()`: 重置环境

### 训练数据管理
- `save_training_data()`: 保存训练数据到JSON文件
- `load_training_data()`: 加载历史训练数据
- `record_episode_end()`: 记录回合结束数据
- `get_training_stats()`: 获取训练统计信息

### 可视化增强
- `_draw_debug_info()`: 绘制增强的训练信息界面
- `_draw_training_charts()`: 绘制实时训练曲线图表
- `get_performance_summary()`: 获取性能总结

### 训练控制
- `pause_training()`: 暂停训练
- `resume_training()`: 恢复训练
- `stop_training()`: 停止训练并保存数据

### 多实例架构
- `MultiTrainingManager`: 多实例训练管理器
- `step_all()`: 对所有实例执行训练步骤
- `render_all()`: 渲染所有实例
- `handle_events()`: 处理多实例事件
- `get_summary_stats()`: 获取所有实例的汇总统计
- `close_all()`: 关闭所有实例

## 多实例训练特性

### 共享物理空间架构
- 所有训练实例在同一个pymunk.Space中运行
- 通过collision_type和collision_filter确保不同实例的代理不会碰撞
- 共享地面和重力设置，提高性能

### 视觉区分系统
- 主实例(ID=0)：使用正常颜色显示
- 其他实例：使用渐变的浅色显示，便于区分但不干扰观察
- 实时显示每个实例的训练状态和位置信息

### 性能优化
- 单一物理空间减少计算开销
- 智能渲染：主实例负责地面和UI，其他实例只绘制代理
- 独立的训练数据管理，每个实例保存到不同目录

## 自定义训练

可以将此环境集成到您的强化学习训练循环中：

```python
env = WalkingEnvironment()
state = env.reset()

for episode in range(num_episodes):
    while not done:
        action = your_agent.get_action(state)
        state, reward, done, info = env.step(action)
        # 训练您的智能体
```

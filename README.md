# 2D双足行走强化学习仿真环境

基于Pygame和Pymunk实现的2D火柴人行走仿真环境，兼容强化学习算法接口。

## 功能特点

- 基于物理引擎的真实2D双足行走仿真
- 类似OpenAI Gym的环境接口
- **增强的可视化界面**：实时显示训练进展、统计信息和奖励曲线
- **训练数据管理**：自动保存和加载训练历史
- **优化的奖励函数**：强化前进速度奖励，降低稳定性约束
- **实时训练监控**：显示成功率、平均奖励、最佳记录等关键指标
- 完整的状态观测和奖励系统
- 支持强化学习算法训练

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行仿真

```bash
python main.py
```

## 控制说明

- 按 `R` 键重置环境
- 按 `P` 键暂停/恢复训练
- 按 `S` 键保存训练数据
- 按 `ESC` 键或关闭窗口退出程序

## 环境接口

### 动作空间
- 4维连续动作空间，范围 [-1, 1]
- 分别对应：左髋、右髋、左膝、右膝的目标角速度

### 状态空间
- 16维状态向量，包含：
  - 躯干角度和角速度
  - 躯干位置和速度
  - 4个关节的相对角度和角速度
  - 双脚接地状态

### 奖励函数 (优化版本)
- **强化前进速度奖励** (权重 0.5)：鼓励快速前进
- **前进距离奖励**：累积前进距离的额外奖励
- **速度方向奖励**：额外奖励向前运动，惩罚后退
- 生存奖励：基础生存奖励
- **降低动作平滑性惩罚** (权重 0.0001)：允许更大幅度动作
- **降低直立性奖励** (权重 0.2)：减少对稳定性的过度关注

### 终止条件
- 头部或躯干接触地面
- 达到最大步数限制

## 代码结构

### 核心环境类
- `WalkingEnvironment`: 主要的环境类
- `_create_agent()`: 创建火柴人代理
- `_create_joints()`: 创建关节和电机系统
- `step()`: 执行一步仿真
- `render()`: 渲染可视化
- `reset()`: 重置环境

### 训练数据管理
- `save_training_data()`: 保存训练数据到JSON文件
- `load_training_data()`: 加载历史训练数据
- `record_episode_end()`: 记录回合结束数据
- `get_training_stats()`: 获取训练统计信息

### 可视化增强
- `_draw_debug_info()`: 绘制增强的训练信息界面
- `_draw_training_charts()`: 绘制实时训练曲线图表
- `get_performance_summary()`: 获取性能总结

### 训练控制
- `pause_training()`: 暂停训练
- `resume_training()`: 恢复训练
- `stop_training()`: 停止训练并保存数据

## 自定义训练

可以将此环境集成到您的强化学习训练循环中：

```python
env = WalkingEnvironment()
state = env.reset()

for episode in range(num_episodes):
    while not done:
        action = your_agent.get_action(state)
        state, reward, done, info = env.step(action)
        # 训练您的智能体
```

# 2D双足行走强化学习仿真环境

基于Pygame和Pymunk实现的2D火柴人行走仿真环境，兼容强化学习算法接口。

## 功能特点

- 基于物理引擎的真实2D双足行走仿真
- 类似OpenAI Gym的环境接口
- 可视化渲染，实时显示仿真过程
- 完整的状态观测和奖励系统
- 支持强化学习算法训练

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行仿真

```bash
python main.py
```

## 控制说明

- 按 `R` 键重置环境
- 按 `ESC` 或关闭窗口退出程序

## 环境接口

### 动作空间
- 4维连续动作空间，范围 [-1, 1]
- 分别对应：左髋、右髋、左膝、右膝的目标角速度

### 状态空间
- 16维状态向量，包含：
  - 躯干角度和角速度
  - 躯干位置和速度
  - 4个关节的相对角度和角速度
  - 双脚接地状态

### 奖励函数
- 前进速度奖励
- 生存奖励
- 动作平滑性奖励
- 保持直立奖励

### 终止条件
- 头部或躯干接触地面
- 躯干过度倾斜(超过60度)
- 达到最大步数限制

## 代码结构

- `WalkingEnvironment`: 主要的环境类
- `_create_agent()`: 创建火柴人代理
- `_create_joints()`: 创建关节和电机系统
- `step()`: 执行一步仿真
- `render()`: 渲染可视化
- `reset()`: 重置环境

## 自定义训练

可以将此环境集成到您的强化学习训练循环中：

```python
env = WalkingEnvironment()
state = env.reset()

for episode in range(num_episodes):
    while not done:
        action = your_agent.get_action(state)
        state, reward, done, info = env.step(action)
        # 训练您的智能体
```

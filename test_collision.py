#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
碰撞检测测试脚本
"""

from main import WalkingEnvironment
import numpy as np
import pygame

def test_ground_collision():
    """测试地面碰撞是否正常工作"""
    print("测试地面碰撞...")
    
    # 创建环境
    env = WalkingEnvironment()
    
    # 重置环境
    state = env.reset()
    
    print("初始状态:")
    if 'torso' in env.agent_parts:
        torso_body = env.agent_parts['torso'][0]
        print(f"  躯干位置: ({torso_body.position.x:.1f}, {torso_body.position.y:.1f})")
        print(f"  地面位置: y = {env.ground_y}")
        print(f"  距离地面: {env.ground_y - torso_body.position.y:.1f} 像素")
    
    # 运行仿真，观察代理是否会掉落并与地面碰撞
    print("\n开始仿真...")
    step_count = 0
    collision_detected = False
    
    try:
        while step_count < 500:  # 运行500步
            # 不施加任何动作，让代理自由掉落
            action = np.zeros(4)
            
            # 执行步骤
            state, reward, done, info = env.step(action)
            
            # 渲染
            env.render()
            env.clock.tick(60)
            
            # 检查是否有身体部件接触地面
            if 'torso' in env.agent_parts:
                torso_body = env.agent_parts['torso'][0]
                
                # 检查是否接近地面
                if torso_body.position.y >= env.ground_y - 50:
                    if not collision_detected:
                        print(f"  步数 {step_count}: 代理接近地面")
                        print(f"    躯干位置: ({torso_body.position.x:.1f}, {torso_body.position.y:.1f})")
                        print(f"    躯干速度: ({torso_body.velocity.x:.1f}, {torso_body.velocity.y:.1f})")
                        collision_detected = True
                
                # 如果代理完全停止移动，说明碰撞正常
                if abs(torso_body.velocity.y) < 0.1 and torso_body.position.y >= env.ground_y - 100:
                    print(f"  步数 {step_count}: 代理已稳定在地面上")
                    print(f"    最终位置: ({torso_body.position.x:.1f}, {torso_body.position.y:.1f})")
                    print("  ✅ 地面碰撞正常工作！")
                    break
            
            step_count += 1
            
            # 处理退出事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    env.close()
                    return
                    
        if step_count >= 500:
            print("  ⚠️  500步后仍未检测到稳定的地面碰撞")
            if 'torso' in env.agent_parts:
                torso_body = env.agent_parts['torso'][0]
                print(f"    最终位置: ({torso_body.position.x:.1f}, {torso_body.position.y:.1f})")
                print(f"    最终速度: ({torso_body.velocity.x:.1f}, {torso_body.velocity.y:.1f})")
                
    except KeyboardInterrupt:
        print("  测试被用户中断")
    finally:
        env.close()
        
    print("碰撞测试完成")

def test_multi_instance_collision():
    """测试多实例模式下的碰撞隔离"""
    print("\n测试多实例碰撞隔离...")
    
    from main import MultiTrainingManager
    
    # 创建2个实例的管理器
    manager = MultiTrainingManager(num_instances=2, show_all_in_one_window=True)
    
    print("已创建2个实例，测试它们是否会相互碰撞...")
    
    try:
        step_count = 0
        while step_count < 200:  # 运行200步
            # 让两个实例都执行动作
            manager.step_all()
            
            # 渲染
            manager.render_all()
            manager.clock.tick(60)
            
            # 检查两个实例的位置
            if step_count % 50 == 0:
                print(f"  步数 {step_count}:")
                for i, env in enumerate(manager.environments):
                    if 'torso' in env.agent_parts:
                        torso_body = env.agent_parts['torso'][0]
                        print(f"    实例 {i}: ({torso_body.position.x:.1f}, {torso_body.position.y:.1f})")
            
            step_count += 1
            
        print("  ✅ 多实例碰撞隔离测试完成")
        
    except KeyboardInterrupt:
        print("  测试被用户中断")
    finally:
        manager.close_all()

if __name__ == "__main__":
    test_ground_collision()
    test_multi_instance_collision()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多实例训练测试脚本
"""

from main import MultiTrainingManager
import time

def test_multi_instance():
    """测试多实例功能"""
    print("测试多实例训练系统...")
    
    # 创建4个实例的管理器
    manager = MultiTrainingManager(num_instances=4, show_all_in_one_window=True)
    
    print("已创建4个训练实例，共享同一个物理空间")
    print("主实例(ID=0)使用正常颜色，其他实例使用更浅的颜色")
    print("所有代理在同一个场景中，但不会相互碰撞")
    
    try:
        step_count = 0
        while step_count < 1000:  # 运行1000步测试
            # 执行训练步骤
            manager.step_all()
            
            # 渲染
            manager.render_all()
            
            # 控制帧率
            manager.clock.tick(60)
            
            step_count += 1
            
            # 每100步打印一次状态
            if step_count % 100 == 0:
                stats = manager.get_summary_stats()
                print(f"步数: {step_count}, 总回合: {stats['total_episodes']}, "
                      f"平均奖励: {stats['avg_recent_reward']:.2f}, "
                      f"活跃实例: {stats['active_instances']}")
                
    except KeyboardInterrupt:
        print("测试中断")
    finally:
        manager.close_all()
        print("测试完成")

if __name__ == "__main__":
    test_multi_instance()
